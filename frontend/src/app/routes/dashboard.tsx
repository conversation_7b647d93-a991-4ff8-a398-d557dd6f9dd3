import clsx from 'clsx'
import { useMemo, useState, useRef, useCallback, useEffect } from 'react'
import { Link, useNavigate } from 'react-router'

import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import Sidebar from '@/components/sidebar'
import RightSidebar from '@/components/right-sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import ShareConversation from '@/components/agent/share-conversation'
import {
    selectSessions,
    selectSessionsLoading,
    selectSessionsHasMore,
    selectSessionsPage,
    selectSessionsLimit,
    fetchSessions,
    resetPagination,
    useAppSelector,
    useAppDispatch,
    toggleFavorite as toggleFavoriteAction,
    selectFavoriteSessionIds
} from '@/state'

enum TAB {
    ALL = 'All',
    RECENT = 'Recent',
    FAVORITE = 'Favorite'
}

export function DashboardPage() {
    const navigate = useNavigate()
    const dispatch = useAppDispatch()
    const [activeTab, setActiveTab] = useState(TAB.ALL)
    const [loadingMore, setLoadingMore] = useState(false)
    const [shareSessionId, setShareSessionId] = useState<string | null>(null)
    const scrollContainerRef = useRef<HTMLDivElement>(null)

    const sessions = useAppSelector(selectSessions)
    const isLoading = useAppSelector(selectSessionsLoading)
    const hasMore = useAppSelector(selectSessionsHasMore)
    const currentPage = useAppSelector(selectSessionsPage)
    const limit = useAppSelector(selectSessionsLimit)
    const favoriteSessionIds = useAppSelector(selectFavoriteSessionIds)

    const handleBack = () => {
        navigate(-1)
    }

    const toggleFavorite = (id: string) => {
        dispatch(toggleFavoriteAction(id))
    }

    const isFavorite = (id: string) => {
        return favoriteSessionIds.includes(id)
    }

    const handleShare = (sessionId: string) => {
        setShareSessionId(sessionId)
    }

    const handleRename = (sessionId: string) => {
        console.log('Rename session:', sessionId)
    }

    const handleDelete = (sessionId: string) => {
        console.log('Delete session:', sessionId)
    }

    const sessionsByTab = useMemo(() => {
        const filteredSessions = sessions?.filter((session) => {
            return session.name
        })
        switch (activeTab) {
            case TAB.ALL:
                return filteredSessions
            case TAB.RECENT:
                return filteredSessions?.slice(0, 10)
            case TAB.FAVORITE:
                return filteredSessions?.filter((session) => {
                    return isFavorite(session.id)
                })
            default:
                return filteredSessions
        }
    }, [activeTab, sessions, favoriteSessionIds])

    // Initialize sessions on mount
    useEffect(() => {
        dispatch(resetPagination())
        dispatch(fetchSessions({ page: 1, limit }))
    }, [dispatch, limit])

    // Handle infinite scroll
    const handleScroll = useCallback(() => {
        if (
            !scrollContainerRef.current ||
            loadingMore ||
            !hasMore ||
            isLoading
        ) {
            return
        }

        const { scrollTop, scrollHeight, clientHeight } =
            scrollContainerRef.current

        // Load more when user scrolls to within 100px of the bottom
        if (scrollHeight - scrollTop - clientHeight < 100) {
            setLoadingMore(true)
            dispatch(fetchSessions({ page: currentPage + 1, limit })).finally(
                () => setLoadingMore(false)
            )
        }
    }, [dispatch, currentPage, limit, hasMore, isLoading, loadingMore])

    useEffect(() => {
        const scrollContainer = scrollContainerRef.current
        if (!scrollContainer) return

        scrollContainer.addEventListener('scroll', handleScroll)
        return () => scrollContainer.removeEventListener('scroll', handleScroll)
    }, [handleScroll])

    return (
        <div className="flex h-screen">
            <div>
                <SidebarProvider>
                    <Sidebar />
                </SidebarProvider>
            </div>
            <div className="flex justify-center pt-[96px] flex-1">
                <div className="flex flex-col gap-y-10 w-full max-w-[768px]">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-x-4">
                            <button
                                className="cursor-pointer"
                                onClick={handleBack}
                            >
                                <Icon
                                    name="arrow-left"
                                    className="size-8 hidden dark:inline"
                                />
                                <Icon
                                    name="arrow-left-dark"
                                    className="size-8 inline dark:hidden"
                                />
                            </button>
                            <span className="text-black dark:text-sky-blue text-[32px] font-bold">
                                Dashboard
                            </span>
                        </div>
                        <div className="flex items-center gap-x-2">
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.ALL,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.ALL
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.ALL)}
                            >
                                All
                            </Button>
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.RECENT,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.RECENT
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.RECENT)}
                            >
                                Recent
                            </Button>
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.FAVORITE,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.FAVORITE
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.FAVORITE)}
                            >
                                Favorite
                            </Button>
                        </div>
                    </div>
                    <div
                        ref={scrollContainerRef}
                        className="flex-1 divide-y divide-black/30 dark:divide-white/30 overflow-auto pb-8"
                    >
                        {sessionsByTab?.map((session) => (
                            <Link
                                to={`/${session.id}`}
                                key={session.id}
                                className="flex items-center justify-between px-4 py-3"
                            >
                                <div className="flex items-center gap-4">
                                    <Icon
                                        name="folder-3"
                                        className="size-10 fill-black dark:fill-white"
                                    />
                                    <div className="flex flex-col gap-1 text-sm">
                                        <p className="font-bold">
                                            {session.name}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-4">
                                    <Button
                                        size="icon"
                                        className="w-auto"
                                        onClick={() =>
                                            toggleFavorite(session.id)
                                        }
                                    >
                                        {isFavorite(session.id) ? (
                                            <Icon
                                                name="star-fill"
                                                className="fill-black dark:fill-white size-6"
                                            />
                                        ) : (
                                            <Icon
                                                name="star"
                                                className="fill-black dark:fill-white size-6"
                                            />
                                        )}
                                    </Button>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger className="cursor-pointer">
                                            <Icon
                                                name="more"
                                                className="size-6 fill-black dark:fill-white"
                                            />
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent
                                            align="end"
                                            className="w-[185px] px-4 py-2"
                                        >
                                            <DropdownMenuItem
                                                className="py-2"
                                                onClick={() =>
                                                    handleShare(session.id)
                                                }
                                            >
                                                <Icon
                                                    name="share"
                                                    className="size-5 stroke-black"
                                                />
                                                Share
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                className="py-2"
                                                onClick={() =>
                                                    handleRename(session.id)
                                                }
                                            >
                                                <Icon
                                                    name="edit"
                                                    className="size-5 fill-black"
                                                />
                                                Rename
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator className="my-1" />
                                            <DropdownMenuItem
                                                onClick={() =>
                                                    handleDelete(session.id)
                                                }
                                                variant="destructive"
                                                className="text-red-2 py-2"
                                            >
                                                <Icon
                                                    name="trash"
                                                    className="size-5"
                                                />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </Link>
                        ))}
                        {loadingMore && (
                            <div className="text-center py-4 text-gray-500">
                                Loading more...
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <RightSidebar />
            {shareSessionId && (
                <ShareConversation
                    open={!!shareSessionId}
                    onOpenChange={(open) => !open && setShareSessionId(null)}
                    sessionId={shareSessionId}
                />
            )}
        </div>
    )
}

export const Component = DashboardPage
