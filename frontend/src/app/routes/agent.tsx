import Lottie from 'lottie-react'
import { useCallback, useEffect, useState } from 'react'
import { useParams } from 'react-router'

import <PERSON><PERSON><PERSON>ie from '@/assets/thinking.json'
import AgentBuild from '@/components/agent/agent-build'
import AgentSteps from '@/components/agent/agent-step'
import AgentTabs from '@/components/agent/agent-tab'
import AgentTasks from '@/components/agent/agent-task'
import Cha<PERSON><PERSON><PERSON> from '@/components/agent/chat-box'
import Agent<PERSON>eader from '@/components/agent/header'
import RightSidebar from '@/components/right-sidebar'
import { sessionService } from '@/services/session.service'
import {
    selectActiveTab,
    selectSelectedBuildStep,
    selectVscodeUrl,
    selectIsSandboxIframeAwake,
    setSelectedFeature,
    useAppDispatch,
    useAppSelector,
    selectIsLoading
} from '@/state'
import { BUILD_STEP, ISession, TAB } from '@/typings/agent'
import AgentResult from '@/components/agent/agent-result'
import AgentPopoverDone from '@/components/agent/agent-popover-done'
import { useSocketIOContext } from '@/contexts/websocket-context'
import AwakeMeUpScreen from '@/components/agent/awake-me-up-screen'

function AgentPageContent() {
    const { sessionId } = useParams()
    const dispatch = useAppDispatch()

    const activeTab = useAppSelector(selectActiveTab)
    const vscodeUrl = useAppSelector(selectVscodeUrl)
    const selectedBuildStep = useAppSelector(selectSelectedBuildStep)
    const isSandboxIframeAwake = useAppSelector(selectIsSandboxIframeAwake)
    const [sessionData, setSessionData] = useState<ISession>()
    const [iframeKey, setIframeKey] = useState(0)
    const [isAwakeLoading, setIsAwakeLoading] = useState(false)
    const { socket } = useSocketIOContext()
    const isRunning = useAppSelector(selectIsLoading)

    const isE2bLink = (url: string): boolean => {
        try {
            const parsed = new URL(url)
            return (
                parsed.hostname.includes('e2b') ||
                parsed.hostname.includes('e2b-')
            )
        } catch {
            return false
        }
    }

    const handleAwakeClick = useCallback(() => {
        setIsAwakeLoading(true)
        if (socket?.connected) {
            socket.emit('chat_message', { type: 'awake_sandbox' })
        }
    }, [socket])

    const handleRefresh = useCallback(() => {
        setIframeKey((prev) => prev + 1)
        if (socket?.connected) {
            socket.emit('chat_message', { type: 'sandbox_status' })
        }
    }, [socket])

    useEffect(() => {
        if (activeTab === TAB.CODE) {
            handleRefresh()
        }
    }, [activeTab, handleRefresh])

    useEffect(() => {
        if (isSandboxIframeAwake) {
            setIsAwakeLoading(false)
        }
    }, [isSandboxIframeAwake])

    useEffect(() => {
        let timeoutId: NodeJS.Timeout | undefined

        const fetchSession = async () => {
            if (sessionId) {
                const data = await sessionService.getSession(sessionId)

                if (!data?.name || data.name.trim() === '') {
                    // Retry after 5 seconds if name is null or empty
                    timeoutId = setTimeout(() => {
                        fetchSession()
                    }, 5000)
                } else {
                    dispatch(setSelectedFeature(data.agent_type))
                    setSessionData(data)
                }
            }
        }

        fetchSession()

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId)
            }
        }
    }, [sessionId])

    useEffect(() => {
        if (isSandboxIframeAwake) {
            setIframeKey((prev) => prev + 1)
        }
    }, [isSandboxIframeAwake])

    return (
        <div className="flex h-screen">
            <div className="flex-1">
                <AgentHeader sessionData={sessionData} />
                <div className="flex !h-[calc(100vh-53px)]">
                    <div
                        className={`flex-1 flex items-center justify-center ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? '' : 'hidden'}`}
                    >
                        {isRunning ? (
                            <div className="flex flex-col items-center justify-center">
                                <Lottie
                                    animationData={ThinkingLottie}
                                    loop={true}
                                />
                                <p className="text-[32px] font-bold  text-black dark:text-sky-blue">
                                    I’m thinking ...
                                </p>
                                <p className="text-2xl text-black dark:text-sky-blue mt-1">
                                    get back to you in minutes
                                </p>
                            </div>
                        ) : (
                            <div className="flex-1" />
                        )}
                    </div>
                    <div
                        className={`flex-1 flex flex-col h-full relative ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? 'hidden' : ''}`}
                    >
                        <AgentTabs />
                        <div className="flex-1">
                            <div
                                className={
                                    activeTab === TAB.BUILD
                                        ? 'h-full'
                                        : 'hidden h-full'
                                }
                            >
                                <div
                                    className={`flex flex-col items-center justify-between p-6 pb-8 h-full`}
                                >
                                    <AgentSteps />
                                    <div
                                        className={`flex flex-1 flex-col justify-between w-full ${selectedBuildStep === BUILD_STEP.PLAN ? '' : 'hidden'}`}
                                    >
                                        <AgentTasks className="flex-1" />
                                        <div />
                                    </div>
                                    <AgentBuild
                                        className={
                                            selectedBuildStep ===
                                            BUILD_STEP.BUILD
                                                ? ''
                                                : 'hidden'
                                        }
                                    />
                                </div>
                            </div>

                            <div
                                className={`h-full ${activeTab === TAB.CODE ? '' : 'hidden'}`}
                            >
                                {vscodeUrl &&
                                isE2bLink(vscodeUrl) &&
                                !isSandboxIframeAwake &&
                                !isRunning ? (
                                    <AwakeMeUpScreen
                                        isLoading={isAwakeLoading}
                                        onAwakeClick={handleAwakeClick}
                                    />
                                ) : vscodeUrl ? (
                                    <iframe
                                        key={iframeKey}
                                        src={vscodeUrl}
                                        className="w-full h-full"
                                    />
                                ) : null}
                            </div>

                            <div
                                className={`h-full relative ${activeTab === TAB.RESULT ? '' : 'hidden'}`}
                            >
                                <AgentResult />
                                <div className="absolute bottom-8 right-4">
                                    <AgentPopoverDone />
                                </div>
                            </div>
                        </div>
                    </div>
                    <ChatBox />
                </div>
            </div>
            <RightSidebar />
        </div>
    )
}

export function AgentPage() {
    return <AgentPageContent />
}

export const Component = AgentPage
