import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { toast } from 'sonner'

import { useSocketIOContext } from '@/contexts/websocket-context'
import {
    selectActiveTab,
    selectIsLoading,
    selectIsSandboxIframeAwake,
    selectMessages,
    selectResultUrl,
    useAppSelector
} from '@/state'
import { TAB, TOOL } from '@/typings/agent'
import { SlidesViewer } from '../slides-viewer'
import { Icon } from '../ui/icon'
import AwakeMeUpScreen from './awake-me-up-screen'

interface AgentResultProps {
    className?: string
}

const AgentResult = ({ className }: AgentResultProps) => {
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const fullscreenContainerRef = useRef<HTMLDivElement>(null)
    const fullscreenIframeRef = useRef<HTMLIFrameElement>(null)
    const [iframeKey, setIframeKey] = useState(0)
    const [isLoading, setIsLoading] = useState(false)
    const [isFullscreenOpen, setIsFullscreenOpen] = useState(false)
    const [currentSlideIndex, setCurrentSlideIndex] = useState(0)
    const [isTransitioning, setIsTransitioning] = useState(false)
    const [localSlideContent, setLocalSlideContent] = useState<
        typeof slideContent
    >([])
    const { socket } = useSocketIOContext()

    const resultUrl = useAppSelector(selectResultUrl)
    const activeTab = useAppSelector(selectActiveTab)
    const isSandboxIframeAwake = useAppSelector(selectIsSandboxIframeAwake)
    const messages = useAppSelector(selectMessages)
    const isRunning = useAppSelector(selectIsLoading)

    // Extract slide data from SlideWrite and SlideEdit messages
    const slideContent = useMemo(() => {
        const slidesMap = new Map<number, string>()

        messages
            .filter(
                (message) =>
                    message.action?.type === TOOL.SLIDE_WRITE ||
                    message.action?.type === TOOL.SLIDE_EDIT
            )
            .forEach((message, index) => {
                let content = (
                    message.action?.data?.result as { content: string }
                )?.content

                if (Array.isArray(message.action?.data?.result)) {
                    content = (
                        message.action?.data?.result as {
                            new_content: string
                        }[]
                    )[0]?.new_content
                }

                if (content) {
                    // Extract slide number from tool input if available
                    const slideNumber =
                        message.action?.data?.tool_input?.slide_number ||
                        index + 1

                    // Update the content for this slide number (overwrites if duplicate)
                    slidesMap.set(slideNumber, content)
                }
            })

        // Convert map to array, sorted by slide number
        return Array.from(slidesMap.entries())
            .sort(([a], [b]) => a - b)
            .map(([slideNumber, content]) => ({
                slideNumber,
                content
            }))
    }, [messages])

    // Extract presentation name from slide tool messages
    const presentationName = useMemo(() => {
        const slideMessage = messages.find(
            (message) =>
                message.action?.type === TOOL.SLIDE_WRITE ||
                message.action?.type === TOOL.SLIDE_EDIT
        )

        return (
            slideMessage?.action?.data?.tool_input?.presentation_name ||
            'Presentation'
        )
    }, [messages])

    const hasSlideTools = useMemo(() => slideContent.length > 0, [slideContent])

    // Initialize local slide content when slideContent changes
    useEffect(() => {
        setLocalSlideContent(slideContent)
    }, [slideContent])

    // Use local slide content for display, falling back to original slideContent
    const displaySlideContent =
        localSlideContent.length > 0 ? localSlideContent : slideContent

    const handleSlideContentUpdate = (
        slideIndex: number,
        slideNumber: number,
        content: string
    ) => {
        setLocalSlideContent((prev) => {
            const updated = [...prev]
            updated[slideIndex] = {
                slideNumber,
                content
            }
            return updated
        })
    }

    const handleCopy = () => {
        if (!resultUrl) return
        navigator.clipboard.writeText(resultUrl)
        toast.success('Copied to clipboard')
    }

    const handleRefresh = () => {
        setIframeKey((prev) => prev + 1)
        if (socket?.connected) {
            socket.emit('chat_message', { type: 'sandbox_status' })
        }
    }

    const isE2bLink = (url: string): boolean => {
        try {
            const parsed = new URL(url)
            return (
                parsed.hostname.includes('e2b') ||
                parsed.hostname.includes('e2b-')
            )
        } catch {
            return false
        }
    }

    const detectUrlType = (url: string): 'website' | 'image' | 'video' => {
        try {
            const parsed = new URL(url)
            const pathname = parsed.pathname.toLowerCase()

            // Common image extensions
            const imageExt = [
                '.png',
                '.jpg',
                '.jpeg',
                '.gif',
                '.bmp',
                '.webp',
                '.svg'
            ]
            // Common video extensions
            const videoExt = [
                '.mp4',
                '.mov',
                '.avi',
                '.mkv',
                '.webm',
                '.flv',
                '.wmv'
            ]

            if (imageExt.some((ext) => pathname.endsWith(ext))) {
                return 'image'
            }
            if (videoExt.some((ext) => pathname.endsWith(ext))) {
                return 'video'
            }

            return 'website'
        } catch {
            return 'website' // fallback if URL is invalid
        }
    }

    const handleAwakeClick = () => {
        setIsLoading(true)
        if (socket?.connected) {
            socket.emit('chat_message', { type: 'awake_sandbox' })
        }
    }

    const handlePresent = async () => {
        if (!fullscreenContainerRef.current) return

        try {
            await fullscreenContainerRef.current.requestFullscreen()
            setIsFullscreenOpen(true)
            setCurrentSlideIndex(0)
        } catch (error) {
            console.error('Failed to enter fullscreen:', error)
            toast.error('Failed to enter fullscreen mode')
        }
    }

    const handleNextSlide = useCallback(() => {
        if (isTransitioning) return
        const nextIndex = Math.min(
            currentSlideIndex + 1,
            displaySlideContent.length - 1
        )
        if (nextIndex === currentSlideIndex) return

        setIsTransitioning(true)
        setCurrentSlideIndex(nextIndex)

        setTimeout(() => {
            setIsTransitioning(false)
        }, 300)
    }, [displaySlideContent.length, currentSlideIndex, isTransitioning])

    const handlePrevSlide = useCallback(() => {
        if (isTransitioning) return
        const prevIndex = Math.max(currentSlideIndex - 1, 0)
        if (prevIndex === currentSlideIndex) return

        setIsTransitioning(true)
        setCurrentSlideIndex(prevIndex)

        setTimeout(() => {
            setIsTransitioning(false)
        }, 300)
    }, [currentSlideIndex, isTransitioning])

    const handleKeyDown = useCallback(
        (event: KeyboardEvent) => {
            if (!isFullscreenOpen) return

            switch (event.key) {
                case 'ArrowRight':
                case ' ':
                    event.preventDefault()
                    handleNextSlide()
                    break
                case 'ArrowLeft':
                    event.preventDefault()
                    handlePrevSlide()
                    break
                case 'Escape':
                    event.preventDefault()
                    document.exitFullscreen()
                    break
            }
        },
        [isFullscreenOpen, handleNextSlide, handlePrevSlide]
    )

    const handleFullscreenChange = useCallback(() => {
        if (!document.fullscreenElement) {
            setIsFullscreenOpen(false)
        }
    }, [])

    const scaleIframeToFitHeight = (
        iframe: HTMLIFrameElement | null,
        opts: {
            designWidth?: number
            allowUpscale?: boolean
            padding?: number
        } = {}
    ) => {
        if (!iframe || !iframe.contentDocument) return 1

        const { designWidth = 1280, allowUpscale = true, padding = 0 } = opts

        const doc = iframe.contentDocument

        // Measure iframe content height (natural height)
        const contentHeight = Math.max(
            doc.body.scrollHeight,
            doc.body.offsetHeight,
            doc.documentElement.offsetHeight
        )

        const viewportHeight = Math.max(0, window.innerHeight - padding)

        // Compute scale factor by height
        let scale = viewportHeight / contentHeight
        if (!allowUpscale) scale = Math.min(scale, 1)

        // Apply scaling to the iframe itself
        iframe.style.transformOrigin = 'top center'
        iframe.style.transform = `scale(${scale})`

        // Force iframe base dimensions (before scaling)
        iframe.style.width = `${designWidth}px`
        iframe.style.height = `${contentHeight}px`

        // Center horizontally
        iframe.style.margin = '0 auto'
        iframe.style.display = 'block'

        return scale
    }

    const shouldShowAwakeScreen = useMemo(() => {
        return isE2bLink(resultUrl) && !isSandboxIframeAwake && !isRunning
    }, [resultUrl, isSandboxIframeAwake, isRunning])

    useEffect(() => {
        if (activeTab === TAB.RESULT) {
            handleRefresh()
        }
    }, [activeTab])

    useEffect(() => {
        if (isSandboxIframeAwake) {
            setIsLoading(false)
        }
    }, [isSandboxIframeAwake])

    useEffect(() => {
        window.addEventListener('keydown', handleKeyDown)
        document.addEventListener('fullscreenchange', handleFullscreenChange)
        return () => {
            window.removeEventListener('keydown', handleKeyDown)
            document.removeEventListener(
                'fullscreenchange',
                handleFullscreenChange
            )
        }
    }, [handleKeyDown, handleFullscreenChange])

    // Calculate scale for fullscreen slides to fit full height
    useEffect(() => {
        const iframe = fullscreenIframeRef.current
        if (!iframe || !isFullscreenOpen) return

        const applyScale = () =>
            setTimeout(
                () => scaleIframeToFitHeight(iframe, { designWidth: 1280 }),
                200
            )

        applyScale()
        window.addEventListener('resize', applyScale)

        return () => {
            iframe.removeEventListener('load', applyScale)
            window.removeEventListener('resize', applyScale)
        }
    }, [isFullscreenOpen, currentSlideIndex])

    if (hasSlideTools) {
        return (
            <div
                className={`flex-1 w-full h-full bg-white dark:bg-charcoal ${className}`}
            >
                <div className="w-full flex items-center justify-between pl-6 pr-4 py-2 gap-4 overflow-hidden border-b border-white/30">
                    <div className="rounded-lg w-full flex items-center gap-4 group transition-colors">
                        <button
                            className="cursor-pointer"
                            onClick={handleRefresh}
                        >
                            <Icon
                                name="refresh"
                                className="size-5 stroke-black dark:stroke-white"
                            />
                        </button>
                        <span className="text-sm text-black bg-[#f4f4f4] dark:bg-white line-clamp-1 break-all flex-1 font-bold px-4 py-1 rounded-sm">
                            {presentationName}
                        </span>
                    </div>
                    <div className="flex items-center gap-4">
                        <button
                            className="cursor-pointer"
                            onClick={handlePresent}
                        >
                            <Icon
                                name="fullscreen"
                                className="size-5 fill-black dark:fill-white"
                            />
                        </button>
                    </div>
                </div>
                <SlidesViewer
                    slides={displaySlideContent}
                    onSlideContentChange={handleSlideContentUpdate}
                    className="h-hull max-h-[calc(100vh-159px)] overflow-auto"
                />
                {/* Fullscreen container */}
                <div
                    ref={fullscreenContainerRef}
                    className={`${
                        isFullscreenOpen
                            ? 'fixed inset-0 z-[9999] bg-black'
                            : 'hidden'
                    }`}
                >
                    <div className="relative w-full h-full flex items-center justify-center">
                        <div className="absolute top-4 right-4 z-10 bg-black/50 text-white px-3 py-1 rounded text-sm">
                            {currentSlideIndex + 1} /{' '}
                            {displaySlideContent.length}
                        </div>

                        <div className="flex items-center gap-4 absolute z-10 bottom-4 right-4">
                            <button
                                onClick={handlePrevSlide}
                                disabled={currentSlideIndex === 0}
                                className="z-10 p-2 text-white bg-black cursor-pointer opacity-30 hover:opacity-100 rounded-full disabled:cursor-not-allowed"
                            >
                                <Icon
                                    name="arrow-left-2"
                                    className="size-6 fill-white"
                                />
                            </button>

                            <button
                                onClick={handleNextSlide}
                                disabled={
                                    currentSlideIndex ===
                                    displaySlideContent.length - 1
                                }
                                className="z-10 p-2 text-white bg-black cursor-pointer opacity-30 hover:opacity-100 rounded-full disabled:cursor-not-allowed"
                            >
                                <Icon
                                    name="arrow-left-2"
                                    className="size-6 fill-white rotate-180"
                                />
                            </button>
                        </div>

                        {/* Close button */}
                        <button
                            onClick={() => document.exitFullscreen()}
                            className="absolute top-4 left-4 z-10 p-2 text-white hover:bg-white/20 rounded-full"
                        >
                            <Icon name="x" className="size-6 fill-white" />
                        </button>

                        {/* Slide container with animations */}
                        <div className="w-full h-full relative overflow-hidden">
                            <div
                                className="w-full h-full flex transition-transform duration-300 ease-in-out"
                                style={{
                                    transform: `translateX(-${currentSlideIndex * 100}%)`
                                }}
                            >
                                {displaySlideContent.map((slide, index) => (
                                    <div
                                        key={`slide-${slide.slideNumber}`}
                                        className="w-full h-full flex-shrink-0 flex items-start justify-center"
                                    >
                                        <iframe
                                            ref={
                                                index === currentSlideIndex
                                                    ? fullscreenIframeRef
                                                    : undefined
                                            }
                                            srcDoc={slide.content}
                                            className="border-0"
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    if (!resultUrl) return null

    if (shouldShowAwakeScreen)
        return (
            <AwakeMeUpScreen
                isLoading={isLoading}
                onAwakeClick={handleAwakeClick}
            />
        )

    return (
        <div
            className={`flex flex-col items-center w-full h-full bg-white dark:bg-charcoal ${className}`}
        >
            <div className="w-full flex items-center justify-between pl-6 pr-4 py-2 gap-4 overflow-hidden border-b border-white/30">
                <div className="rounded-lg w-full flex items-center gap-4 group transition-colors">
                    <button className="cursor-pointer" onClick={handleRefresh}>
                        <Icon
                            name="refresh"
                            className="size-5 stroke-black dark:stroke-white"
                        />
                    </button>
                    <span className="text-sm text-black bg-[#f4f4f4] dark:bg-white line-clamp-1 break-all flex-1 font-bold px-4 py-1 rounded-sm">
                        {resultUrl}
                    </span>
                </div>
                <div className="flex items-center gap-4">
                    <button className="cursor-pointer" onClick={handleCopy}>
                        <Icon
                            name="copy"
                            className="size-5 fill-black dark:fill-white"
                        />
                    </button>
                    <button
                        className="cursor-pointer"
                        onClick={() => window.open(resultUrl, '_blank')}
                    >
                        <Icon
                            name="maximize"
                            className="size-5 fill-black dark:fill-white"
                        />
                    </button>
                </div>
            </div>
            {detectUrlType(resultUrl) === 'image' ? (
                <img
                    src={resultUrl}
                    className="w-full h-full object-contain object-top flex-1"
                />
            ) : detectUrlType(resultUrl) === 'video' ? (
                <video
                    loop
                    muted
                    controls
                    src={resultUrl}
                    className="w-full h-full object-contain object-top flex-1"
                />
            ) : (
                <iframe
                    key={iframeKey}
                    ref={iframeRef}
                    src={resultUrl}
                    className="w-full h-full flex-1"
                />
            )}
        </div>
    )
}

export default AgentResult
