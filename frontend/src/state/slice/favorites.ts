import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { RootState } from '../store'

interface FavoritesState {
    favoriteSessionIds: string[]
}

const initialState: FavoritesState = {
    favoriteSessionIds: []
}

const favoritesSlice = createSlice({
    name: 'favorites',
    initialState,
    reducers: {
        toggleFavorite: (state, action: PayloadAction<string>) => {
            const sessionId = action.payload
            const index = state.favoriteSessionIds.indexOf(sessionId)
            
            if (index > -1) {
                state.favoriteSessionIds.splice(index, 1)
            } else {
                state.favoriteSessionIds.push(sessionId)
            }
        },
        addFavorite: (state, action: PayloadAction<string>) => {
            const sessionId = action.payload
            if (!state.favoriteSessionIds.includes(sessionId)) {
                state.favoriteSessionIds.push(sessionId)
            }
        },
        removeFavorite: (state, action: PayloadAction<string>) => {
            const sessionId = action.payload
            state.favoriteSessionIds = state.favoriteSessionIds.filter(
                id => id !== sessionId
            )
        },
        clearFavorites: (state) => {
            state.favoriteSessionIds = []
        }
    }
})

export const { toggleFavorite, addFavorite, removeFavorite, clearFavorites } = favoritesSlice.actions

export const selectFavoriteSessionIds = (state: RootState) => state.favorites.favoriteSessionIds
export const selectIsFavorite = (sessionId: string) => (state: RootState) => 
    state.favorites.favoriteSessionIds.includes(sessionId)

export const favoritesReducer = favoritesSlice.reducer