"""Agent service for managing agent lifecycle."""

import logging
import uuid
from typing import Dict, Any, List, Optional

from ii_agent.agents.function_call import FunctionCallAgent
from ii_agent.controller.agent_controller import AgentController
from ii_agent.controller.state import State
from ii_agent.core.config.agent_config import AgentConfig
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.event_stream import AsyncEventStream
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.storage.base import BaseStorage
from ii_agent.llm.context_manager import LLMCompact
from ii_agent.llm.token_counter import TokenCounter
from ii_agent.prompts.agent_prompts import get_system_prompt_for_agent_type
from ii_agent.config.agent_types import AgentType, AgentTypeConfig
from ii_agent.sub_agent.base import BaseAgentTool
from ii_agent.sub_agent.researcher_agent_tool import ResearcherAgent
from ii_agent.sub_agent.task_agent_tool import TaskAgentTool
from ii_agent.utils.workspace_manager import WorkspaceManager
from ii_agent.controller.tool_manager import Agent<PERSON><PERSON><PERSON>anager
from ii_agent.llm.base import LLMClient, ToolParam
from ii_tool.sandbox.providers.base import Sandbox
from ii_tool.tools.base import BaseTool
from ii_tool.utils import load_tools_from_mcp
from ii_tool.tools.manager import get_common_tools
logger = logging.getLogger(__name__)


class AgentService:
    """Service for managing agent lifecycle and creation."""

    def __init__(
        self,
        config: IIAgentConfig,
        file_store: BaseStorage,
    ):
        self.config = config
        self.file_store = file_store
    
    
    async def create_task_agent(
        self,
        llm_client: LLMClient,
        tools: List[BaseTool],
        context_manager: ContextManager,
        event_stream: AsyncEventStream,
        max_turns: int = 200,
    ) -> BaseAgentTool:
        from ii_agent.sub_agent.task_agent_tool import SYSTEM_PROMPT as TASK_AGENT_PROMPT
        tools = [
            tool for tool in tools if tool.name in AgentTypeConfig.get_toolset(AgentType.TASK_AGENT)
        ]
        task_agent_config = AgentConfig(
            max_tokens_per_turn=self.config.max_output_tokens_per_turn,
            system_prompt=TASK_AGENT_PROMPT,
        )
        sub_agent = FunctionCallAgent(
            llm=llm_client,
            config=task_agent_config,
            tools=[
                ToolParam(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.input_schema,
                )
                for tool in tools
            ],
        )
        task_agent = TaskAgentTool(
            agent=sub_agent,
            tools=tools,
            context_manager=context_manager,
            event_stream=event_stream,
            max_turns=max_turns,
            config=self.config
        )
        return task_agent
        
    async def create_researcher_agent(
        self,
        client: LLMClient,
        tools: List[BaseTool],
        context_manager: ContextManager,
        event_stream: AsyncEventStream,
        max_turns: int = 200,
    ) -> BaseAgentTool:

        researcher_tool = [
            tool for tool in tools if tool.name in AgentTypeConfig.get_toolset(AgentType.RESEARCHER)
        ]
        researcher_subagent = ResearcherAgent(
            client=client,
            llm_config= self.config.researcher_agent_config,
            tools=researcher_tool,
            context_manager=context_manager,
            event_stream=event_stream,
            max_turns=max_turns,
            config=self.config
        )
        return researcher_subagent

    async def create_agent(
        self,
        llm_client: LLMClient,
        session_id: uuid.UUID,
        sandbox: Sandbox,
        workspace_manager: WorkspaceManager,
        event_stream: AsyncEventStream,
        system_prompt: Optional[str] = None,
        agent_type: AgentType = AgentType.GENERAL,
        tool_args: Optional[Dict[str, Any]] = None,
    ) -> AgentController:
        """Create a new agent instance following CLI patterns.

        Args:
            llm_client: LLM client instance
            session_id: Session UUID
            sandbox_public_port_url_generator: Generator for sandbox public port URL
            workspace_manager: Workspace manager instance
            event_stream: AsyncEventStream for event handling
            tool_args: Tool configuration arguments
            system_prompt: Optional custom system prompt

        Returns:
            AgentController: The controller for the created agent
        """
        # Create context manager
        token_counter = TokenCounter()
        context_manager = LLMCompact(
            client=llm_client,
            token_counter=token_counter,
            token_budget=self.config.token_budget,
        )
        mcp_sandbox_url = await sandbox.expose_port(self.config.mcp_port) + "/mcp/"

        # Determine system prompt
        if system_prompt is None:
            system_prompt = get_system_prompt_for_agent_type(
                agent_type, workspace_manager.root.absolute().as_posix()
            )

        # Create agent config
        agent_config = AgentConfig(
            max_tokens_per_turn=self.config.max_output_tokens_per_turn,
            system_prompt=system_prompt,
            temperature=getattr(self.config, "temperature", 0.7),
        )

        # Create tool manager and register tools
        tool_manager = AgentToolManager()

        # Get core common tools
        all_common_tools = get_common_tools(
            sandbox=sandbox
        )

        # Get core sandbox tools
        all_sandbox_tools = await load_tools_from_mcp(
            mcp_sandbox_url, timeout=self.config.mcp_timeout
        )

        # Filter tools based on agent type
        forbidden_tool_names = AgentTypeConfig.get_forbidden_toolset(agent_type)

        if tool_args is not None and tool_args.get('deep_research'):
            researcher_subagent = await self.create_researcher_agent(
                client=llm_client,
                tools=all_sandbox_tools, # type: ignore
                context_manager=context_manager,
                event_stream=event_stream,
            )
            tool_manager.register_tools([researcher_subagent]) 

        task_subagent = await self.create_task_agent(
            llm_client=llm_client,
            tools=all_sandbox_tools,
            context_manager=context_manager,
            event_stream=event_stream,
        )
        tool_manager.register_tools([task_subagent])

        # Register all common and sandbox tools, excluding forbidden ones
        tool_manager.register_tools(
            [
                tool
                for tool in [
                    *all_common_tools,
                    *all_sandbox_tools,
                ]
                if tool.name not in forbidden_tool_names
            ]
        )

        # Create agent with proper tools
        agent = FunctionCallAgent(
            llm=llm_client,
            config=agent_config,
            tools=[
                ToolParam(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.input_schema,
                )
                for tool in tool_manager.get_tools()
            ],
        )



        # Create or restore state
        state = State()
        try:
            state.restore_from_session(str(session_id), self.file_store)
            logger.info(f"Restored state from session {session_id}")
        except FileNotFoundError:
            logger.info(f"No history found for session {session_id}")

        return AgentController(
            agent=agent,
            tool_manager=tool_manager,
            init_history=state,
            event_stream=event_stream,
            context_manager=context_manager,
            interactive_mode=True,
            config=self.config,
        )

    async def create_reviewer_agent(
        self,
        llm_client: LLMClient,
        session_id: uuid.UUID,
        sandbox: Sandbox,
        workspace_manager: WorkspaceManager,
        event_stream: AsyncEventStream,
    ) -> AgentController:
        """Create a reviewer agent using FunctionCallAgent with reviewer prompt.

        Args:
            llm_client: LLM client instance
            session_id: Session UUID
            workspace_manager: Workspace manager instance
            event_stream: AsyncEventStream for event handling
            tool_args: Tool configuration arguments

        Returns:
            AgentController: The controller for the reviewer agent
        """
        reviewer_prompt = self.get_reviewer_system_prompt()

        return await self.create_agent(
            llm_client=llm_client,
            session_id=session_id,
            sandbox=sandbox,
            workspace_manager=workspace_manager,
            event_stream=event_stream,
            system_prompt=reviewer_prompt,
        )

    def get_reviewer_system_prompt(self) -> str:
        """Get the system prompt for reviewer functionality."""
        return """You are a reviewer agent tasked with evaluating the work done by a general agent. 
You have access to all the same tools that the general agent has.

Focus on:
- Testing ALL interactive elements (buttons, forms, navigation, etc.)
- Verifying functionality and user experience  
- Providing detailed, natural language feedback
- Identifying specific issues and areas for improvement

The user will provide you with the task, result, and workspace directory to review. Conduct a thorough review with emphasis on functionality testing and user experience evaluation."""
