"""Agent type configuration and enums."""

from enum import Enum
from typing import List

# from ii_tool.mcp_integrations.playwright import SELECTED_TOOLS as PLAYWRIGHT_TOOLS
from ii_tool.tools.dev.database import GetDatabaseConnection
from ii_tool.tools.shell import (
    ShellView,
    ShellInit,
    ShellKill,
    ShellStopCommand,
    ShellList,
    ShellRunCommand,
)
from ii_tool.tools.file_system import (
    GlobTool,
    GrepTool,
    FileEditTool,
    FileReadTool,
    FileWriteTool,
    LSTool,
    MultiEditTool,
)
from ii_tool.tools.browser import (
    <PERSON>rowserClickTool,
    BrowserWaitTool,
    BrowserViewTool,
    BrowserScrollDownTool,
    BrowserScrollUpTool,
    BrowserSwitchTabTool,
    BrowserOpenNewTabTool,
    BrowserGetSelectOptionsTool,
    BrowserSelectDropdownOptionTool,
    BrowserNavigationTool,
    <PERSON>rowserRestartTool,
    <PERSON>rowserEnterTextTool,
    <PERSON>rowser<PERSON><PERSON><PERSON><PERSON>Tool,
)
from ii_tool.tools.media import <PERSON><PERSON>enerate<PERSON>ool, <PERSON><PERSON>enerateTool
from ii_tool.tools.dev import <PERSON>Port, FullStackInitTool
from ii_tool.tools.web import WebSearchTool, WebVisitTool, WebVisitCompressTool
from ii_tool.tools.slide_system.slide_edit_tool import SlideEditTool
from ii_tool.tools.slide_system.slide_write_tool import SlideWriteTool
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.web.image_search_tool import ImageSearchTool
from ii_tool.tools.web.web_batch_search_tool import WebBatchSearchTool


class AgentType(str, Enum):
    """Enumeration of available agent types."""

    GENERAL = "general"
    MEDIA = "media"
    SLIDE = "slide"
    RESEARCHER = "researcher"
    WEBSITE_BUILD = "website_build"
    TASK_AGENT = "task_agent"

class AgentTypeConfig:
    """Configuration for different agent types."""

    # Define toolsets for each agent type
    TOOLSETS = {
        AgentType.GENERAL: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellKill.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            GlobTool.name,
            GrepTool.name,
            LSTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            MultiEditTool.name,
            FullStackInitTool.name,
            # Media tools
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            RegisterPort.name,
            FullStackInitTool.name,
            SlideWriteTool.name,
            SlideEditTool.name,
            # Browser tools
            BrowserClickTool.name,
            BrowserWaitTool.name,
            BrowserViewTool.name,
            BrowserScrollDownTool.name,
            BrowserScrollUpTool.name,
            BrowserSwitchTabTool.name,
            BrowserOpenNewTabTool.name,
            BrowserGetSelectOptionsTool.name,
            BrowserSelectDropdownOptionTool.name,
            BrowserNavigationTool.name,
            BrowserRestartTool.name,
            BrowserEnterTextTool.name,
            BrowserPressKeyTool.name,
            # *PLAYWRIGHT_TOOLS,
        ],
        AgentType.TASK_AGENT: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellKill.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            GlobTool.name,
            GrepTool.name,
            LSTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            MultiEditTool.name,
            FullStackInitTool.name,
            # Media tools
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            # Browser tools
            BrowserClickTool.name,
            BrowserWaitTool.name,
            BrowserViewTool.name,
            BrowserScrollDownTool.name,
            BrowserScrollUpTool.name,
            BrowserSwitchTabTool.name,
            BrowserOpenNewTabTool.name,
            BrowserGetSelectOptionsTool.name,
            BrowserSelectDropdownOptionTool.name,
            BrowserNavigationTool.name,
            BrowserRestartTool.name,
            BrowserEnterTextTool.name,
            BrowserPressKeyTool.name,
            # *PLAYWRIGHT_TOOLS,
        ],
        AgentType.MEDIA: [
            # File system tools
            FileReadTool.name,
            LSTool.name,
            # Media tools
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            # Todo tools
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.SLIDE: [
            # TODO: Add slide-specific tools when available
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            ImageSearchTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            SlideWriteTool.name,
            SlideEditTool.name,
        ],
        AgentType.WEBSITE_BUILD: [
            # Shell tools
            ShellInit.name,
            ShellRunCommand.name,
            ShellView.name,
            ShellKill.name,
            ShellStopCommand.name,
            ShellList.name,
            # File system tools
            GlobTool.name,
            GrepTool.name,
            LSTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            FileEditTool.name,
            MultiEditTool.name,
            FullStackInitTool.name,
            # Media tools
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            # Web tools
            WebSearchTool.name,
            WebVisitTool.name,
            # Todo tools
            TodoReadTool.name,
            TodoWriteTool.name,
            # common tools
            RegisterPort.name,
            GetDatabaseConnection.name,
            # Browser tools
            BrowserClickTool.name,
            BrowserWaitTool.name,
            BrowserViewTool.name,
            BrowserScrollDownTool.name,
            BrowserScrollUpTool.name,
            BrowserSwitchTabTool.name,
            BrowserOpenNewTabTool.name,
            BrowserGetSelectOptionsTool.name,
            BrowserSelectDropdownOptionTool.name,
            BrowserNavigationTool.name,
            BrowserRestartTool.name,
            BrowserEnterTextTool.name,
            BrowserPressKeyTool.name,
            # *PLAYWRIGHT_TOOLS,
        ],
        AgentType.RESEARCHER: [
            WebBatchSearchTool.name,
            WebVisitCompressTool.name,
        ],
    }

    @classmethod
    def get_toolset(cls, agent_type: AgentType) -> List[str]:
        """Get the toolset for a specific agent type."""
        return cls.TOOLSETS.get(agent_type, cls.TOOLSETS[AgentType.GENERAL])

    @classmethod
    def get_forbidden_toolset(cls, agent_type: AgentType) -> List[str]:
        """Get the forbidden toolset for a specific agent type by excluding the tools."""
        all_tools = set(cls.TOOLSETS[AgentType.GENERAL])
        allowed_tools = set(cls.get_toolset(agent_type))
        return list(all_tools - allowed_tools)

    @classmethod
    def is_valid_agent_type(cls, agent_type: str) -> bool:
        """Check if an agent type is valid."""
        try:
            AgentType(agent_type)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_agent_types(cls) -> List[str]:
        """Get all available agent types."""
        return [agent_type.value for agent_type in AgentType]
