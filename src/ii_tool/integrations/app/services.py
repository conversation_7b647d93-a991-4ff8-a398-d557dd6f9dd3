"""This module contains the services for the tool server."""

from .config import config
from ii_tool.integrations.image_search import create_image_search_client, ImageSearchService
from ii_tool.integrations.storage import create_storage_client

storage = create_storage_client(
    config=config.storage_config,
)
image_search_client = create_image_search_client(config.image_search_config)
image_search_service = ImageSearchService(image_search_client, storage)


__all__ = ["image_search_service"]