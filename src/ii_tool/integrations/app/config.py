from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Literal
from ii_tool.core.config import (
    ImageGenerateConfig,
    ImageSearchConfig,
    VideoGenerateConfig,
    WebSearchConfig,
    WebVisitConfig,
    DatabaseConfig,
    CompressorConfig,
)
from ii_agent.core.config.enhance_prompt_config import EnhancePromptConfig
from ii_tool.integrations.storage.config import StorageConfig

class ToolServerConfig(BaseSettings):
    web_search_config: WebSearchConfig = WebSearchConfig()
    web_visit_config: WebVisitConfig = WebVisitConfig()
    image_search_config: ImageSearchConfig = ImageSearchConfig()
    video_generate_config: VideoGenerateConfig = VideoGenerateConfig()
    image_generate_config: ImageGenerateConfig = ImageGenerateConfig()
    database_config: DatabaseConfig = DatabaseConfig()
    enhance_prompt_config: EnhancePromptConfig = EnhancePromptConfig()
    compressor_config: CompressorConfig = CompressorConfig()
    storage_config: StorageConfig = StorageConfig()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
    )

config = ToolServerConfig()
