import asyncio
from typing import Any, List
import aiohttp

from ii_tool.core.config import CompressorConfig

from .base import BaseWebVisitClient, WebVisitError


class FireCrawlWebVisitClient(BaseWebVisitClient):
    """FireCrawl implementation of web visit client."""
    
    def __init__(self, api_key: str, compressor_config: CompressorConfig):
        self.api_key = api_key
        self.base_url = "https://api.firecrawl.dev/v1/scrape"
        super().__init__(compressor_config)


    async def _extract(self, url: str) -> dict[str, Any]:
        """Visit webpage and extract content using FireCrawl."""

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }
        payload = {
            "url": url,
            "onlyMainContent": False,
            "formats": ["markdown"],
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url, headers=headers, json=payload,
            ) as response:
                response.raise_for_status()
                response_data = await response.json()

        data = response_data.get("data", {})
        return data
    
    async def extract(self, url: str) -> str:
        """Visit webpage and extract content using FireCrawl."""
        data = await self._extract(url)
        markdown = data.get("markdown", "")
        if not markdown:
            raise WebVisitError(
                "No content could be extracted from webpage"
            )
        return markdown
    
    async def extract_compress(self, url: str, query: str) -> str:
        """Visit webpage and extract content using FireCrawl."""
        data = await self._extract(url)
        raw_content = data.get("markdown", "")
        compressed_content = await self.context_compressor.acompress(
            raw_content,
            title=data.get("title", ""),
            query=query,
        )
    
        return_str = ""
        if data.get("title"):
            return_str += f"Title: {data.get('title', '')}\n"
        return_str += f"URL: {url}\n"
        return_str += f"Content: {compressed_content}\n"
        return_str += "-----------------------------------\n"
        return return_str
    
    async def batch_extract_compress(self, urls: List[str], query: str) -> str:
        """Visit webpage and extract content using FireCrawl."""
        tasks = [self.extract_compress(url, query) for url in urls]
        results = await asyncio.gather(*tasks)
        return "\n".join(results)